import { Field, FieldArray, Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import * as Yup from 'yup'

export const mergeSourceAndCampaignNames = (data: any) => {
  return data.flatMap((channel: any) =>
    channel.leadSources.flatMap((source: any) => [
      source.name,
      ...source.campaigns.map((campaign: any) => campaign.name),
    ])
  )
}

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { createClient } from '../../../../logic/apis/client'
import { onlyText } from '../../../../shared/helpers/regex'
import {
  dayjsFormat,
  getDigitsFromPhone,
  getIdFromName,
  getKeysFromObjects,
  getValueByKeyAndMatch,
  isSuccess,
  notify,
  splitFullName,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import Toggle from '../../../../shared/toggle/Toggle'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { I_LeadSource } from '../../../newLead/NewLead'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import AutoCompleteAddress from '../../../../shared/autoCompleteAdress/AutoCompleteAddress'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { Nue, StageGroupEnum, usStatesShortNames } from '../../../../shared/helpers/constants'
import { colors } from '../../../../styles/theme'
import Button from '../../../../shared/components/button/Button'
import '../../../../shared/helpers/yupExtension'
import { getFormattedLeadSrcData, getLeadSrcDropData, getLeadSrcDropdownId } from '../../../leadSource/LeadSource'
import AutoCompleteIndentation from '../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { Types } from '../../constant'
import { getLeadSources } from '../../../../logic/apis/leadSource'
import { createContact, getSearchedContact } from '../../../../logic/apis/contact'
import AutoComplete from '../../../../shared/autoComplete/AutoComplete'
import SearchableDropdown from '../../../../shared/searchableDropdown/SearchableDropdown'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { AddRelationshipContact } from '../addRelationshipContactModal/AddRelationshipContact'
import ContactCard from './components/ContactCard'
import ContactCardToBeAdded from './components/ContactCardToBeAdded'
import { fields } from '../../../formBuilder/constant'
import { IntendWidth } from '../../style'
import { SharedDateAndTime } from '../../../../shared/date/SharedDateAndTime'
import { getStages } from '../../../../logic/apis/sales'
import { I_Stage } from '../../../opportunity/components/assessmentForm/AssessmentForm'
import { getProjectTypes } from '../../../../logic/apis/projects'
import { SLoader } from '../../../../shared/components/loader/Loader'

interface I_AddNewClientModal {
  setShowAddNewClientModal: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  detailsUpdate: boolean
  setClientAutoFill?: any
  setShowReferrerModal?: React.Dispatch<React.SetStateAction<boolean>>
  clientName?: string
  onClose?: () => void
  setCreatedClient?: any
  officeDrop?: any
  // referrerDropdownData?: any
  refererres?: any
  setReferrerValue?: any
  isNewLead?: boolean
  noLoadScript?: boolean
  setClientData?: React.Dispatch<any>
}
export interface I_Contacts {
  email: ''
  firstName: ''
  lastName: ''
  phone: ''
  notes: ''
}
export const AddNewContactModal = (props: I_AddNewClientModal) => {
  const {
    setShowAddNewClientModal,
    setDetailsUpdate,
    detailsUpdate,
    setShowReferrerModal,
    setClientAutoFill,
    clientName,
    onClose,
    officeDrop,
    setCreatedClient,
    // referrerDropdownData,
    refererres,
    setReferrerValue,
    isNewLead,
    noLoadScript,
    setClientData,
  } = props
  const [isBusiness, setIsBusiness] = useState(false)

  /**
   * InitialValues is an interface declared here so that it can be used as a type for the useState hook
   */

  interface InitialValues {
    newLeadDate?: string
    CSRAssigned?: string
    workType?: string
    firstName: string
    lastName: string
    businessName: string
    city: string
    street: string
    state: string
    zip: string
    phone: string
    email: string
    type: string
    contacts: I_Contacts[]
    leadSourceName: string
    referredBy: string
    notes: string

    // businessName?: string
  }

  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    newLeadDate: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
    CSRAssigned: '',
    workType: '',

    firstName: '',
    lastName: '',
    businessName: '',
    city: '',
    street: '',
    state: '',
    zip: '',
    phone: '',
    type: '',
    contacts: [],
    email: '',
    leadSourceName: '',
    referredBy: '',
    notes: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [toggleAddress, setToggleAddress] = useState<boolean>(false)
  const [leadsrcDrop, setLeadsrcDrop] = useState<I_LeadSource[]>([])
  const [leadSrcData, setLeadSrcData] = useState([])
  const [clients, setClients] = useState<I_LeadSource[]>([])
  const [clientsLoader, setClientsLoader] = useState<boolean>(true)
  const [addContact, setAddContact] = useState<boolean>(false)
  const [relationshipContact, setAddRelationshipContact] = useState<any>({})
  const [showAddContact, setShowAddContact] = useState<boolean>(false)
  const [checkedIndex, setCheckedIndex] = useState(null)
  const [searchValue, setSearchValue] = useState('')
  const [contactIndex, setContactIndex] = useState(-1)
  const [defaultStage, setDefaultStage] = useState('') // will be stage id
  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])

  const globalSelector = useSelector((state: any) => state)

  const { currentMember, companySettingForAll } = globalSelector.company

  const fetchSearchContact = async (query: string) => {
    try {
      const res = await getSearchedContact(query, {
        fields: {
          fullName: 1,
          firstName: 1,
          lastName: 1,
          email: 1,
          phone: 1,
          notes: 1,
          street: 1,
          city: 1,
          state: 1,
          zip: 1,
        },
      })
      if (isSuccess(res)) {
        console.log({ res })
        return res // ✅ return the successful result
      } else {
        return null // or [] or undefined depending on expected return shape
      }
    } catch (error) {
      console.error({ error })
      return null // or [] or undefined
    }
  }

  useEffect(() => {
    if (leadSrcData?.length) {
      const data = getLeadSrcDropData(leadSrcData)
      setLeadsrcDrop(data)
    }
  }, [leadSrcData?.length])

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])

  /**
   * AddCityModalSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */

  const AddCityModalSchema = Yup.object().shape({
    businessName: isBusiness
      ? Yup.string().min(1, 'Too Short!').max(50, 'Too Long!').required('Required')
      : Yup.string().min(1, 'Too Short!').max(50, 'Too Long!'),
    firstName: Yup.string().required('Required'),
    lastName: Yup.string().min(1, 'Too Short!').max(50, 'Too Long!'),
    leadSourceName: Yup.string().required('Required'),
    type: !isNewLead ? Yup.string().required('Required') : Yup.string(),
    notes: Yup.string(),
    phone: Yup.string().test('phone-or-email', 'Either phone or email is required', function (value) {
      return value || this.parent.email
    }),
    email: Yup.string()
      .email('Invalid email')
      .test('phone-or-email', 'Either phone or email is required', function (value) {
        return value || this.parent.phone
      }),
    workType: isNewLead ? Yup.string().required('Required') : Yup.string(),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    const result = getLeadSrcDropdownId(submittedValues?.leadSourceName, leadSrcData)

    try {
      const leadsourceId = result?.leadSourceId
      const campaignId = result?.campaignId || null
      const referredById =
        leadsourceId === '8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6'
          ? getIdFromName(submittedValues?.referredBy, refererres)
          : null
      setLoading(true)
      let { email, contacts, leadSourceName, ...restSubmitted }: any = submittedValues

      // remove cEmails key if empty
      if (email) restSubmitted.email = email

      let phone = getDigitsFromPhone(submittedValues.phone)
      const linkedContacts = Array.isArray(contacts)
        ? contacts.map(({ id, relationship }: { id: string; relationship: string }) => ({
            id,
            relationship,
          }))
        : []

      let dataObj = {
        ...restSubmitted,
        fullName: `${submittedValues?.firstName?.trim()} ${submittedValues?.lastName?.trim() || ''}`?.trim(),
        firstName: submittedValues?.firstName?.trim(),
        lastName: submittedValues?.lastName?.trim() || '',
        businessName: isBusiness ? submittedValues?.businessName?.trim() : undefined,
        phone,
        type: isNewLead ? 'lead' : Types[submittedValues.type as keyof typeof Types],
        linkedContacts,
        leadSourceId: leadsourceId,
        workType: isNewLead
          ? getValueByKeyAndMatch('id', submittedValues?.workType, 'name', projectTypesDrop)
          : undefined,
        newLeadDate: isNewLead ? submittedValues.newLeadDate : undefined,
        csrId: isNewLead ? getValueByKeyAndMatch('_id', submittedValues?.CSRAssigned, 'name', officeDrop) : undefined,
        createdBy: currentMember._id,
        referredBy: referredById ? referredById : null,
        isBusiness,
        campaignId,
        stageId: isNewLead ? defaultStage : null,
        status: isNewLead ? 'active' : undefined,
      }
      console.log({ dataObj })

      let response = await createContact(dataObj)
      if (isSuccess(response)) {
        setClientAutoFill?.({
          phone: undefined,
          email: undefined,
          clientName: `${submittedValues?.firstName} ${submittedValues?.lastName || ''}`?.trim(),
          sStreet: submittedValues?.street,
          sCity: submittedValues?.city,
          sState: submittedValues?.state,
          sZip: submittedValues?.zip,
          sLeadSourceName: submittedValues?.leadSourceName,
          sLeadSourceId: leadsourceId,
          sCampaignId: campaignId,
          type: Types[submittedValues.type as keyof typeof Types] || undefined,
          referredBy: referredById,
        })

        notify('Contact Added Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowAddNewClientModal(false)
        setCreatedClient(response?.data?.data?.data)
        setClientData(response?.data?.data?.data)
        onClose?.()
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('AddNewClientModal handleSubmit', error)
    }
  }

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false, StageGroupEnum.Leads)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        setInitialValues((pre) => ({
          ...pre,
          CSRAssigned: getValueByKeyAndMatch('name', stages[0]?.defaultCsrId, '_id', officeDrop),
        }))

        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
          if (stage.code === 'newLead') setDefaultStage(stage._id)
        })
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  const initFetchProjectType = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))

        setProjectTypesDrop([
          ...object,
          {
            name: 'Unknown',
            id: 'unknown',
            value: 'unknown',
            label: 'unknown',
          },
        ])
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  useEffect(() => {
    if (isNewLead) {
      initFetchProjectType()
      getStagesData()
    }
  }, [])

  useEffect(() => {
    getLeadSrcData()
  }, [detailsUpdate])

  // useEffect(() => {
  //   if (!clientsLoader) {
  //     if (clients?.length > 1) {
  //       handleCheckboxChange(0, clients[0])
  //     } else {
  //       handleCheckboxChange(-1)
  //     }
  //   }
  // }, [clientsLoader])
  // const handleCheckboxChange = (index: number, client?: any) => {
  //   if (checkedIndex === index) {
  //     // If the same checkbox is clicked again, uncheck it
  //     setCheckedIndex(null)
  //     setClientAutoFill?.({}) // Reset selected value
  //   } else {
  //     // Otherwise, select the new checkbox
  //     setCheckedIndex(index)
  //     setCreatedClient(client)
  //     setClientAutoFill?.({
  //       clientName: `${client?.firstName} ${client?.lastName || ''}`?.trim(),
  //       sStreet: client?.street,
  //       sCity: client?.city,
  //       sState: client?.state,
  //       sZip: client?.zip,
  //       sLeadSourceName: client?.leadSourceName,
  //       sLeadSource: client?.leadSource,
  //       referredBy: client?.referredBy,
  //       phone: client?.phone,
  //       email: client?.email,
  //     })
  //   }
  // }

  return (
    <Styled.AddNewClientModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={AddCityModalSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange }) => {
          const handleRemoveToBeAdded = (index: number, contacts: any[]) => {
            const updated = [...contacts]
            updated.splice(index, 1)
            setFieldValue('contacts', updated || [])
          }

          useEffect(() => {
            if (values.contacts.length > 0) {
              setFieldValue('type', 'Linked')
            } else {
              setFieldValue('type', '')
            }
          }, [values.contacts])
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Add New Contact</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setCreatedClient?.({})
                    setClientAutoFill?.({
                      clientName: '',
                      sStreet: '',
                      sCity: '',
                      sState: '',
                      sZip: '',
                      sLeadSourceName: '',
                      sLeadSource: '',
                      referredBy: '',
                      phone: '',
                      email: '',
                    })
                    setShowAddNewClientModal(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>

              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content
                    maxWidth="706px"
                    width="100%"
                    gap="6px"
                    disableBoxShadow={true}
                    noPadding={true}
                  >
                    {isNewLead ? (
                      <SharedDateAndTime
                        value={values.newLeadDate}
                        labelName="Date/Time lead 
                            came in *"
                        stateName="newLeadDate"
                        setFieldValue={setFieldValue}
                        error={touched.newLeadDate && errors.newLeadDate ? true : false}
                      />
                    ) : null}

                    <Toggle
                      title="Business"
                      customStyles={{ margin: '16px' }}
                      isToggled={isBusiness}
                      onToggle={() => {
                        setIsBusiness((prev) => !prev)
                      }}
                    />

                    {isBusiness ? (
                      <InputWithValidation
                        labelName="Business Name*"
                        stateName="businessName"
                        error={touched.businessName && errors.businessName ? true : false}
                        twoInput={true}
                      />
                    ) : null}
                    <SharedStyled.TwoInputDiv>
                      <InputWithValidation
                        labelName="Primary First Name*"
                        stateName="firstName"
                        error={touched.firstName && errors.firstName ? true : false}
                        twoInput={true}
                      />
                      <InputWithValidation
                        labelName="Primary Last Name"
                        stateName="lastName"
                        error={touched.lastName && errors.lastName ? true : false}
                        twoInput={true}
                      />
                    </SharedStyled.TwoInputDiv>

                    <SharedStyled.TwoInputDiv>
                      <SharedPhone
                        labelName="Primary Phone"
                        stateName="phone"
                        value={values.phone || ''}
                        onChange={handleChange('phone')}
                        error={touched.phone && errors.phone ? true : false}
                      />

                      <InputWithValidation
                        labelName="Primary Email"
                        stateName="email"
                        error={touched.email && errors.email ? true : false}
                      />
                    </SharedStyled.TwoInputDiv>

                    <SharedStyled.FlexCol gap="6px">
                      <br />
                      <div>
                        {' '}
                        <SharedStyled.Text
                          color={`${colors.darkGrey}`}
                          textAlign="left"
                          fontWeight="bold"
                          fontSize="16px"
                        >
                          Linked Contacts{' '}
                        </SharedStyled.Text>
                        <Button
                          padding="4px 10px"
                          width="max-content"
                          type="button"
                          onClick={() => {
                            setShowAddContact((pre) => !pre)
                          }}
                        >
                          +
                        </Button>
                      </div>
                      {showAddContact ? (
                        <IntendWidth>
                          <SharedStyled.FlexCol width="100%" gap="10px" alignItems="center">
                            <SearchableDropdown
                              label="Search Contacts"
                              placeholder="Type to search"
                              searchFunction={fetchSearchContact}
                              displayKey={'fullName'}
                              onSelect={(item: any) => {
                                setAddRelationshipContact(item)
                                setAddContact(true)
                                setContactIndex(values?.contacts?.length || 0)
                              }}
                              resultExtractor={(res) => res?.data?.data?.contacts || []}
                              onAddClick={() => {
                                setAddContact(true)
                                setContactIndex(-1)
                              }}
                              addNewText="Add New"
                              showAddOption
                            />
                            {addContact && (
                              <AddRelationshipContact
                                onClose={() => {
                                  setAddContact(false)
                                  setAddRelationshipContact({})
                                }}
                                companySettingForAll={companySettingForAll}
                                contacts={values.contacts}
                                setFieldValue={setFieldValue}
                                index={contactIndex}
                                relationshipContact={relationshipContact}
                              />
                            )}
                          </SharedStyled.FlexCol>
                          {values?.contacts?.map((v, index: number) => (
                            <ContactCardToBeAdded
                              contact={v}
                              key={index}
                              onRemove={() => handleRemoveToBeAdded(index, values?.contacts)}
                              onClick={() => {
                                setContactIndex(index)
                                setAddContact(true)
                              }}
                            />
                          ))}
                        </IntendWidth>
                      ) : null}
                    </SharedStyled.FlexCol>

                    {!isNewLead ? (
                      <CustomSelect
                        value={values.type}
                        labelName="Type*"
                        stateName="type"
                        dropDownData={Object.keys(Types)}
                        setFieldValue={setFieldValue}
                        setValue={() => {}}
                        margin="10px 0 0 0"
                        error={touched.type && errors.type ? true : false}
                      />
                    ) : null}

                    {leadSrcData?.length ? (
                      <AutoCompleteIndentation
                        labelName="Lead Source*"
                        stateName={`leadSourceName`}
                        isLeadSource
                        dropdownHeight="300px"
                        error={touched.leadSourceName && errors.leadSourceName ? true : false}
                        borderRadius="0px"
                        setFieldValue={setFieldValue}
                        options={mergeSourceAndCampaignNames(leadSrcData)}
                        formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                        value={values.leadSourceName!}
                        setValueOnClick={(val: string) => {
                          setFieldValue('leadSourceName', val)
                        }}
                        className="material-autocomplete"
                        isIndentation={true}
                      />
                    ) : (
                      <SLoader height={40} width={100} isPercent />
                    )}

                    {values.leadSourceName === 'Referral' && (
                      <SharedStyled.FlexBox width="100%" justifyContent="end">
                        <CustomSelect
                          labelName="Referrer"
                          stateName="referredBy"
                          error={touched.referredBy && errors.referredBy ? true : false}
                          setFieldValue={setFieldValue}
                          setValue={setReferrerValue}
                          value={values.referredBy}
                          dropDownData={refererres?.map((item: any) => item?.name || '')}
                          innerHeight="52px"
                          className="top"
                          maxWidth="95%"
                          showAddOption
                          addNewText="--Add New--"
                          onAddClick={() => {
                            setShowReferrerModal(true)
                          }}
                        />
                      </SharedStyled.FlexBox>
                    )}

                    {isNewLead ? (
                      <CustomSelect
                        labelName="Type of work requested *"
                        stateName="workType"
                        error={touched.workType && errors.workType ? true : false}
                        setFieldValue={setFieldValue}
                        value={values.workType}
                        dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name)]}
                        setValue={() => {}}
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />
                    ) : null}
                    {isNewLead ? (
                      <CustomSelect
                        labelName="CSR Assigned"
                        stateName="CSRAssigned"
                        error={touched.CSRAssigned && errors.CSRAssigned ? true : false}
                        setFieldValue={setFieldValue}
                        value={values.CSRAssigned}
                        dropDownData={getKeysFromObjects(officeDrop, 'name')}
                        setValue={() => {}}
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />
                    ) : null}

                    <Styled.GoogleSearchBox>
                      <AutoCompleteAddress
                        setFieldValue={setFieldValue}
                        street={'street'}
                        city={'city'}
                        state={'state'}
                        zip={'zip'}
                        sourceAddress={companySettingForAll?.address}
                        companyLatLong={companySettingForAll}
                        noLoadScript={noLoadScript ? noLoadScript : false}
                      />
                    </Styled.GoogleSearchBox>

                    {!toggleAddress ? (
                      <div style={{ width: '100%' }}>
                        {values.street !== '' || values.city !== '' || values.state !== '' || values.zip !== '' ? (
                          <>
                            <SharedStyled.Text fontWeight="400">
                              <b>Address: </b> <br />
                              <span style={{ fontFamily: Nue.regular }}>
                                {values.street !== '' ? values.street : '--'}
                              </span>
                            </SharedStyled.Text>
                            <br />
                            <SharedStyled.Text fontWeight="400">
                              <span style={{ fontFamily: Nue.regular }}>{values.city},&nbsp;</span>

                              <span style={{ fontFamily: Nue.regular }}>{values.state},&nbsp;</span>

                              <span style={{ fontFamily: Nue.regular }}>{values.zip}</span>
                            </SharedStyled.Text>
                            &emsp;
                            <span
                              style={{ cursor: 'pointer', color: colors.darkBlue, fontFamily: Nue.medium }}
                              className="link"
                              onClick={() => setToggleAddress(!toggleAddress)}
                            >
                              Edit Manually
                            </span>
                          </>
                        ) : (
                          <>
                            <span
                              style={{ cursor: 'pointer', color: colors.darkBlue, fontFamily: Nue.medium }}
                              className="link"
                              onClick={() => setToggleAddress(!toggleAddress)}
                            >
                              Edit Manually
                            </span>
                          </>
                        )}
                      </div>
                    ) : (
                      <>
                        <InputWithValidation
                          labelName="Street Address"
                          stateName="street"
                          error={touched.street && errors.street ? true : false}
                        />

                        <SharedStyled.TwoInputDiv>
                          <InputWithValidation
                            labelName="City"
                            stateName="city"
                            error={touched.city && errors.city ? true : false}
                            twoInput={true}
                          />
                          <CustomSelect
                            dropDownData={usStatesShortNames}
                            setValue={() => {}}
                            stateName="state"
                            value={values.state}
                            setFieldValue={setFieldValue}
                            labelName="State"
                            margin="8px 0 0 0"
                          />
                          <InputWithValidation
                            labelName="Zip"
                            stateName="zip"
                            error={touched.zip && errors.zip ? true : false}
                          />
                        </SharedStyled.TwoInputDiv>
                      </>
                    )}

                    <Styled.TextArea
                      component="textarea"
                      placeholder="Notes"
                      as={Field}
                      name="notes"
                      marginTop="8px"
                      height="52px"
                    ></Styled.TextArea>
                    <SharedStyled.ButtonContainer marginTop="26px">
                      <Button type="submit" isLoading={loading}>
                        Add
                      </Button>
                      <Button
                        type="button"
                        className="delete"
                        onClick={() => {
                          setShowAddNewClientModal(false)
                          setClientAutoFill?.({})
                        }}
                      >
                        Close
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.AddNewClientModalContainer>
  )
}
