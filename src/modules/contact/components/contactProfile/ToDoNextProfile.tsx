import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../../styles/styled'
import { Form, Formik } from 'formik'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import AutoComplete from '../../../../shared/autoComplete/AutoComplete'
import Button from '../../../../shared/components/button/Button'
import Checkbox from '../../../../shared/checkbox/Checkbox'
import { SharedDateAndTime } from '../../../../shared/date/SharedDateAndTime'

import {
  dayjsFormat,
  generateUUID,
  getKeysFromObjects,
  getValueByKeyAndMatch,
  isSuccess,
  notify,
} from '../../../../shared/helpers/util'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { useSelector } from 'react-redux'
import {
  completeAction,
  completeLeadAction,
  createAction,
  createLeadAction,
  getSalesActionByMemberId,
  updateActivity,
} from '../../../../logic/apis/sales'
import * as Yup from 'yup'
import ActionModal from '../../../opportunity/components/actionModal/ActionModal'

interface I_Action {
  type: string
  body: string
  completedBy: string
  assignTo?: string
  due: string
  _id?: string
}

interface I_FormProps {
  leadData: any
  leadId: string | undefined
  setLeadData: React.Dispatch<React.SetStateAction<any | undefined>>
  fetchActivity: () => Promise<void>
}

interface I_NextAction {
  _id: string
  type: string
  body: string
  due: string
  createdBy: string
  assignTo?: string
  createdAt: string
}

const ToDoNextProfile: React.FC<I_FormProps> = (props) => {
  const [todoCheck, setTodoCheck] = useState(false)
  const [editTodo, setEditTodo] = useState(false)
  const [actionsForToDoNext, setActionsForToDoNext] = useState<any>([])
  const [toDoLoading, setToDoLoading] = useState(false)
  const [actionModal, setActionModal] = useState(false)
  const [autoFillValues, setAutoFillValues] = useState({ type: '', name: '' })
  const [autoFillValuesFromChild, setAutoFillValuesFromChild] = useState({ type: '', name: '' })

  const [initTodoData, setInitTodoData] = useState<I_Action>({
    due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
    type: '',
    completedBy: '',
    body: '',
  })
  const { leadData, leadId, setLeadData, fetchActivity } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentMember } = globalSelector.company

  useEffect(() => {
    if (currentMember?._id) {
      fetchActions()
    }
  }, [currentMember])
  console.log({ autoFillValues })
  const fetchActions = async () => {
    try {
      const res = await getSalesActionByMemberId(currentMember?._id, false)
      if (isSuccess(res)) {
        const { actions } = res.data.data.salesAction
        setActionsForToDoNext(actions)
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const onTodoComplete = async (nextAction: I_Action) => {
    const actionId = actionsForToDoNext?.find((action: { name: string }) => action?.name === nextAction?.body)?._id

    let nextActionData: I_NextAction = {
      _id: actionId ?? generateUUID(),
      body: nextAction.body,
      createdAt: new Date().toISOString(),
      createdBy: currentMember._id,
      due: new Date(nextAction.due).toISOString(),
      type: nextAction.type,
    }

    try {
      setToDoLoading(true)

      if (!todoCheck && leadData?.nextAction) {
        const responseForEdit = await createLeadAction({
          body: nextAction.body,
          currDate: new Date(),
          dueDate: new Date(nextAction.due),
          memberId: currentMember._id!,
          leadId: leadId!,
          type: nextAction.type,
          id: actionId ?? generateUUID(),
        })

        if (isSuccess(responseForEdit)) {
          setLeadData((prev: any) => ({ ...prev, nextAction: nextActionData }))
          if (leadData?.nextAction) {
            const res = await updateActivity({
              id: leadId!,
              memberId: currentMember._id!,
              body: `Edited:  ${leadData?.nextAction.body} to ${nextAction.body} | Due date: ${new Date(
                leadData?.nextAction.due
              ).toLocaleDateString('en-US', {
                month: '2-digit',
                day: '2-digit',
                year: 'numeric',
              })} ${
                leadData?.nextAction.due
                  ? new Date(leadData?.nextAction.due).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: 'numeric',
                    })
                  : ''
              } to ${new Date(nextAction.due).toLocaleDateString('en-US', {
                month: '2-digit',
                day: '2-digit',
                year: 'numeric',
              })} ${new Date(nextAction.due).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: 'numeric',
              })}`,
              currDate: new Date().toISOString(),
            })
            if (isSuccess(res)) {
              notify('Action Updated', 'success')
              fetchActivity()
            }
          }
        } else {
          throw new Error(responseForEdit?.data?.message)
        }
      } else {
        // action api
        if (leadData?.nextAction) {
          await completeLeadAction({
            body: leadData.nextAction.body,
            currDate: new Date(),
            dueDate: leadData.nextAction.due,
            memberId: currentMember._id!, //UserId##
            leadId: leadId!,
            type: leadData.nextAction.type,
            id: leadData?.nextAction?._id,
          })
        }

        const nextActionId = actionsForToDoNext?.find(
          (action: { name: string }) => action?.name === nextAction?.body
        )?._id

        const response = await createLeadAction({
          body: nextAction.body,
          currDate: new Date(),
          dueDate: nextAction.due,
          memberId: currentMember._id!,
          leadId: leadId!,
          type: nextAction.type,
          id: nextActionId ?? generateUUID(),
        })

        if (isSuccess(response)) {
          setLeadData((prev: any) => ({ ...prev, nextAction: nextActionData }))
          if (leadData?.nextAction) {
            const res1 = await updateActivity({
              id: leadId!,
              memberId: currentMember._id!,
              body: `Completed action ${leadData?.nextAction?.body}`,
              currDate: new Date().toISOString(),
            })
            const res2 = await updateActivity({
              id: leadId!,
              memberId: currentMember._id!,
              body: `Created new action ${nextActionData.body}`,
              currDate: new Date().toISOString(),
            })

            if (isSuccess(res1) && isSuccess(res2)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          } else {
            const res = await updateActivity({
              id: leadId!,
              memberId: currentMember._id!,
              body: `Created new action ${nextAction.body}`,
              currDate: new Date().toISOString(),
            })
            if (isSuccess(res)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          }
        } else {
          throw new Error(response?.data?.message)
        }
      }
      setTodoCheck(false)
      setEditTodo(false)
    } catch (err) {
      console.log('ACTIONS ERR', err)
    } finally {
      setToDoLoading(false)
    }
  }

  const onEditCancel = () => {
    setInitTodoData({
      due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
      type: '',
      completedBy: '',
      body: '',
      _id: '',
    })
    if (leadData?.nextAction) {
      setEditTodo(false)
      setTodoCheck(false)
    }
  }

  const onEditTodo = () => {
    if (leadData && leadData?.nextAction)
      setInitTodoData({
        due: dayjsFormat(leadData?.nextAction.due, 'YYYY-MM-DDTHH:mm'),
        body: leadData!.nextAction.body,
        type: leadData!.nextAction.type,
        completedBy: currentMember._id!,
      })

    setEditTodo((prev) => !prev)
  }

  const todoSchema = Yup.object().shape({
    body: Yup.string().required('Required'),
    due: Yup.string().required('Required'),
    type: Yup.string().required('Required'),
  })
  return (
    <>
      <div>
        <SharedStyled.ContentHeader textAlign="left" as="h3">
          To Do Next
        </SharedStyled.ContentHeader>
        {toDoLoading ? (
          <SharedStyled.CardSkeleton height="200px"></SharedStyled.CardSkeleton>
        ) : (
          <div className="todo-item">
            {todoCheck || editTodo || !leadData?.nextAction ? (
              <div style={{ width: '100%' }}>
                <Formik
                  initialValues={initTodoData}
                  onSubmit={onTodoComplete}
                  validationSchema={todoSchema}
                  enableReinitialize={true}
                  validateOnChange={true}
                  validateOnBlur={false}
                >
                  {({ touched, errors, resetForm, values, setFieldValue }) => {
                    useEffect(() => {
                      if (autoFillValuesFromChild.type !== '' && autoFillValuesFromChild.name !== '') {
                        setFieldValue('type', autoFillValuesFromChild.type)
                        setFieldValue('body', autoFillValuesFromChild.name)
                      }
                    }, [autoFillValuesFromChild, leadData])

                    useEffect(() => {
                      if (values.type) {
                        setAutoFillValues((prev) => ({
                          ...prev,
                          type: values.type,
                        }))
                      }
                    }, [values.type])
                    return (
                      <Form>
                        {/* stepObject */}
                        <SharedStyled.TwoInputDiv>
                          <SharedStyled.FlexBox width="100%" gap="12px">
                            <AutoComplete
                              value={values?.body}
                              options={getKeysFromObjects(actionsForToDoNext ?? [], 'name') ?? []}
                              dropdownHeight={'300px'}
                              labelName="Next Action"
                              stateName="body"
                              setFieldValue={setFieldValue}
                              error={touched.body && errors.body ? true : false}
                              onAddClick={(val: string) => {
                                setAutoFillValues((prev) => ({ ...prev, name: val }))
                                setActionModal(true)
                              }}
                              showAddOption
                              setValueOnClick={(val: string) => {
                                setFieldValue(`type`, getValueByKeyAndMatch('type', val, `name`, actionsForToDoNext))
                              }}
                            />
                          </SharedStyled.FlexBox>
                        </SharedStyled.TwoInputDiv>
                        <div style={{ display: 'grid', gap: '10px', gridTemplateColumns: '1fr 2fr' }}>
                          <CustomSelect
                            labelName="Select Type"
                            stateName="type"
                            error={touched.type && errors.type ? true : false}
                            value={values.type}
                            dropDownData={['Task', 'Call', 'Email', 'Text']}
                            setValue={() => {}}
                            setFieldValue={setFieldValue}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />

                          <SharedDateAndTime
                            value={values.due}
                            labelName={'Due Date/Time'}
                            stateName="due"
                            setFieldValue={setFieldValue}
                            error={touched.due && errors.due ? true : false}
                          />
                        </div>

                        <SharedStyled.FlexBox width="100%" gap="12px" margin="24px 0 0 0">
                          <Button type="submit" className="fit">
                            Save Action
                          </Button>
                          <Button onClick={() => onEditCancel()} className="fit outline">
                            Cancel
                          </Button>
                        </SharedStyled.FlexBox>
                      </Form>
                    )
                  }}
                </Formik>
              </div>
            ) : null}
            {leadData?.nextAction && !editTodo ? (
              <div className="todo-container">
                <SharedStyled.FlexRow>
                  <Checkbox
                    onChange={() => {
                      setTodoCheck((prev) => !prev)
                    }}
                    value={todoCheck}
                    cursor="pointer"
                  />
                </SharedStyled.FlexRow>

                <div className="checkbox-item">
                  <div className={todoCheck ? 'strike' : ''}>
                    <SharedStyled.FlexRow gap="12px">
                      <SharedStyled.FlexRow>
                        <p className="bold">{leadData?.nextAction?.body}</p>
                      </SharedStyled.FlexRow>
                      <SharedStyled.FlexRow justifyContent="space-between">
                        <p className="bold">{leadData.nextAction?.type}</p>
                      </SharedStyled.FlexRow>
                      {leadData.nextAction?.due ? (
                        <SharedStyled.FlexRow justifyContent="space-between">
                          <p className="bold">
                            {new Date(leadData?.nextAction?.due).toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric',
                            })}
                          </p>
                        </SharedStyled.FlexRow>
                      ) : (
                        ''
                      )}
                      {leadData.nextAction?.due ? (
                        <SharedStyled.FlexRow justifyContent="space-between">
                          <p className="bold">
                            {new Date(leadData?.nextAction?.due).toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: 'numeric',
                            })}
                          </p>
                        </SharedStyled.FlexRow>
                      ) : (
                        ''
                      )}
                    </SharedStyled.FlexRow>
                  </div>
                </div>

                {!todoCheck && (
                  <span
                    className="link"
                    onClick={() => {
                      onEditTodo()
                    }}
                  >
                    Edit
                  </span>
                )}
              </div>
            ) : null}
          </div>
        )}
      </div>
      <CustomModal show={actionModal}>
        <ActionModal
          onClose={() => setActionModal(false)}
          onComplete={fetchActions}
          values={autoFillValues}
          setAutoFillValuesFromChild={setAutoFillValuesFromChild}
        />
      </CustomModal>
    </>
  )
}

export default ToDoNextProfile
